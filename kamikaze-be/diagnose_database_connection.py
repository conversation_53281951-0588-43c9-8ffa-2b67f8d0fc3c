#!/usr/bin/env python3
"""
Database Connection Diagnostic Tool for Kamikaze AI

This script helps diagnose database connectivity issues by testing:
1. AWS Secrets Manager credential retrieval
2. Network connectivity to the database host
3. Database server accessibility
4. Authentication with retrieved credentials
5. SSL/TLS connection requirements
6. Firewall and security group issues

Usage: python3 diagnose_database_connection.py
"""

import asyncio
import asyncpg
import json
import logging
import os
import socket
import sys
import time
from pathlib import Path

# Add src directory to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class DatabaseDiagnostic:
    def __init__(self):
        self.credentials = None
        self.host = None
        self.port = None
        self.database = None
        self.username = None
        self.password = None
        
    async def run_full_diagnostic(self):
        """Run complete database connectivity diagnostic."""
        logger.info("🔍 Starting Database Connection Diagnostic")
        logger.info("=" * 60)
        
        # Step 1: Test AWS Secrets Manager
        if not await self.test_aws_secrets_manager():
            logger.error("❌ AWS Secrets Manager test failed - cannot proceed")
            return False
            
        # Step 2: Test network connectivity
        if not await self.test_network_connectivity():
            logger.error("❌ Network connectivity test failed")
            return False
            
        # Step 3: Test database server accessibility
        if not await self.test_database_server():
            logger.error("❌ Database server accessibility test failed")
            return False
            
        # Step 4: Test authentication
        if not await self.test_database_authentication():
            logger.error("❌ Database authentication test failed")
            return False
            
        # Step 5: Test SSL requirements
        await self.test_ssl_requirements()
        
        logger.info("✅ All diagnostic tests completed successfully!")
        return True
        
    async def test_aws_secrets_manager(self):
        """Test AWS Secrets Manager credential retrieval."""
        logger.info("🔐 Testing AWS Secrets Manager...")
        
        try:
            from infrastructure.aws_secrets_manager import AWSSecretsManager
            
            secrets_manager = AWSSecretsManager()
            self.credentials = await secrets_manager.get_database_credentials()
            
            if self.credentials:
                self.host = self.credentials.host
                self.port = self.credentials.port
                self.database = self.credentials.database
                self.username = self.credentials.username
                self.password = self.credentials.password
                
                logger.info(f"✅ Successfully retrieved credentials from AWS Secrets Manager")
                logger.info(f"   Host: {self.host}")
                logger.info(f"   Port: {self.port}")
                logger.info(f"   Database: {self.database}")
                logger.info(f"   Username: {self.username}")
                logger.info(f"   Password: {'*' * len(self.password) if self.password else 'None'}")
                logger.info(f"   SSL Mode: {self.credentials.ssl_mode}")
                return True
            else:
                logger.error("❌ Failed to retrieve credentials from AWS Secrets Manager")
                return False
                
        except Exception as e:
            logger.error(f"❌ AWS Secrets Manager error: {e}")
            return False
            
    async def test_network_connectivity(self):
        """Test basic network connectivity to database host."""
        logger.info(f"🌐 Testing network connectivity to {self.host}:{self.port}...")
        
        try:
            # Test DNS resolution
            logger.info(f"   Resolving DNS for {self.host}...")
            ip_address = socket.gethostbyname(self.host)
            logger.info(f"   ✅ DNS resolved: {self.host} -> {ip_address}")
            
            # Test TCP connectivity
            logger.info(f"   Testing TCP connection to {ip_address}:{self.port}...")
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(10)  # 10 second timeout
            
            start_time = time.time()
            result = sock.connect_ex((ip_address, self.port))
            end_time = time.time()
            
            sock.close()
            
            if result == 0:
                logger.info(f"   ✅ TCP connection successful ({end_time - start_time:.2f}s)")
                return True
            else:
                logger.error(f"   ❌ TCP connection failed (error code: {result})")
                logger.error("   This usually indicates:")
                logger.error("   1. Firewall blocking the connection")
                logger.error("   2. Security group not allowing your IP")
                logger.error("   3. Database server not running")
                logger.error("   4. Network routing issues")
                return False
                
        except socket.gaierror as e:
            logger.error(f"   ❌ DNS resolution failed: {e}")
            logger.error("   Check if the hostname is correct")
            return False
        except Exception as e:
            logger.error(f"   ❌ Network connectivity test failed: {e}")
            return False
            
    async def test_database_server(self):
        """Test if database server is responding to PostgreSQL protocol."""
        logger.info("🗄️ Testing database server accessibility...")
        
        try:
            # Try to connect without authentication to test server response
            logger.info("   Testing PostgreSQL protocol response...")
            
            conn = await asyncio.wait_for(
                asyncpg.connect(
                    host=self.host,
                    port=self.port,
                    database=self.database,
                    user="test_user",  # Invalid user to test server response
                    password="test_password",  # Invalid password
                    ssl="prefer"
                ),
                timeout=15
            )
            # If we get here, something unexpected happened
            await conn.close()
            logger.info("   ✅ Database server is responding")
            return True
            
        except asyncpg.InvalidAuthorizationSpecificationError:
            logger.info("   ✅ Database server is responding (authentication required)")
            return True
        except asyncpg.InvalidCatalogNameError:
            logger.error(f"   ❌ Database '{self.database}' does not exist")
            return False
        except asyncio.TimeoutError:
            logger.error("   ❌ Database server connection timed out")
            logger.error("   This usually indicates:")
            logger.error("   1. Database server is not running")
            logger.error("   2. Server is overloaded")
            logger.error("   3. Network latency issues")
            return False
        except Exception as e:
            logger.error(f"   ❌ Database server test failed: {e}")
            return False
            
    async def test_database_authentication(self):
        """Test database authentication with retrieved credentials."""
        logger.info("🔑 Testing database authentication...")
        
        try:
            logger.info("   Attempting connection with retrieved credentials...")
            
            conn = await asyncio.wait_for(
                asyncpg.connect(
                    host=self.host,
                    port=self.port,
                    database=self.database,
                    user=self.username,
                    password=self.password,
                    ssl="prefer"
                ),
                timeout=30  # Longer timeout for authentication
            )
            
            # Test basic query
            result = await conn.fetchval("SELECT version()")
            logger.info(f"   ✅ Authentication successful!")
            logger.info(f"   PostgreSQL version: {result}")
            
            await conn.close()
            return True
            
        except asyncpg.InvalidAuthorizationSpecificationError as e:
            logger.error(f"   ❌ Authentication failed: {e}")
            logger.error("   Check if username/password are correct")
            return False
        except asyncpg.InvalidCatalogNameError as e:
            logger.error(f"   ❌ Database does not exist: {e}")
            return False
        except asyncio.TimeoutError:
            logger.error("   ❌ Authentication timed out")
            return False
        except Exception as e:
            logger.error(f"   ❌ Authentication test failed: {e}")
            return False
            
    async def test_ssl_requirements(self):
        """Test SSL connection requirements."""
        logger.info("🔒 Testing SSL connection requirements...")
        
        ssl_modes = ["disable", "prefer", "require"]
        
        for ssl_mode in ssl_modes:
            try:
                logger.info(f"   Testing SSL mode: {ssl_mode}")
                
                conn = await asyncio.wait_for(
                    asyncpg.connect(
                        host=self.host,
                        port=self.port,
                        database=self.database,
                        user=self.username,
                        password=self.password,
                        ssl=ssl_mode
                    ),
                    timeout=15
                )
                
                await conn.close()
                logger.info(f"   ✅ SSL mode '{ssl_mode}' works")
                
            except Exception as e:
                logger.info(f"   ❌ SSL mode '{ssl_mode}' failed: {e}")

async def main():
    """Main diagnostic function."""
    diagnostic = DatabaseDiagnostic()
    success = await diagnostic.run_full_diagnostic()
    
    if success:
        logger.info("🎉 Database connectivity diagnostic completed successfully!")
        logger.info("Your database should be accessible from the application.")
    else:
        logger.error("💥 Database connectivity issues detected!")
        logger.error("Please address the issues above before running the application.")
        
    return success

if __name__ == "__main__":
    try:
        result = asyncio.run(main())
        sys.exit(0 if result else 1)
    except KeyboardInterrupt:
        logger.info("Diagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Diagnostic failed with unexpected error: {e}")
        sys.exit(1)
