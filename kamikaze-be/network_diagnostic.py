#!/usr/bin/env python3
"""
Network Diagnostic Tool for RDS Database Connection
Tests network connectivity to the RDS instance
"""

import socket
import time
import subprocess
import sys

def test_dns_resolution(hostname):
    """Test DNS resolution"""
    print(f"🔍 Testing DNS resolution for {hostname}...")
    try:
        ip_address = socket.gethostbyname(hostname)
        print(f"✅ DNS resolved: {hostname} -> {ip_address}")
        return ip_address
    except socket.gaierror as e:
        print(f"❌ DNS resolution failed: {e}")
        return None

def test_tcp_connection(host, port, timeout=10):
    """Test TCP connection"""
    print(f"🌐 Testing TCP connection to {host}:{port} (timeout: {timeout}s)...")
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(timeout)
        
        start_time = time.time()
        result = sock.connect_ex((host, port))
        end_time = time.time()
        
        sock.close()
        
        if result == 0:
            print(f"✅ TCP connection successful ({end_time - start_time:.2f}s)")
            return True
        else:
            print(f"❌ TCP connection failed (error code: {result})")
            print("   Common causes:")
            print("   1. Security group doesn't allow connections from your IP")
            print("   2. RDS instance is in a private subnet")
            print("   3. Network ACLs blocking the connection")
            print("   4. Firewall blocking port 5432")
            return False
            
    except socket.timeout:
        print(f"❌ TCP connection timed out after {timeout} seconds")
        return False
    except Exception as e:
        print(f"❌ TCP connection error: {e}")
        return False

def test_ping(hostname):
    """Test ping connectivity"""
    print(f"🏓 Testing ping to {hostname}...")
    try:
        # Use ping command (works on both macOS and Linux)
        result = subprocess.run(
            ['ping', '-c', '3', hostname], 
            capture_output=True, 
            text=True, 
            timeout=15
        )
        
        if result.returncode == 0:
            print("✅ Ping successful")
            # Extract average time from ping output
            lines = result.stdout.split('\n')
            for line in lines:
                if 'avg' in line or 'average' in line:
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ Ping failed")
            print(f"   Error: {result.stderr.strip()}")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Ping timed out")
        return False
    except Exception as e:
        print(f"❌ Ping error: {e}")
        return False

def test_traceroute(hostname):
    """Test traceroute to see network path"""
    print(f"🛣️ Testing traceroute to {hostname}...")
    try:
        # Use traceroute on macOS/Linux
        cmd = ['traceroute', '-m', '10', hostname]  # Max 10 hops
        result = subprocess.run(
            cmd, 
            capture_output=True, 
            text=True, 
            timeout=30
        )
        
        if result.returncode == 0:
            print("✅ Traceroute completed:")
            lines = result.stdout.split('\n')[:8]  # Show first 8 lines
            for line in lines:
                if line.strip():
                    print(f"   {line.strip()}")
            return True
        else:
            print("❌ Traceroute failed")
            return False
            
    except subprocess.TimeoutExpired:
        print("❌ Traceroute timed out")
        return False
    except FileNotFoundError:
        print("⚠️ Traceroute command not found, skipping...")
        return False
    except Exception as e:
        print(f"❌ Traceroute error: {e}")
        return False

def get_public_ip():
    """Get public IP address"""
    print("🌍 Getting your public IP address...")
    try:
        import urllib.request
        response = urllib.request.urlopen('https://api.ipify.org', timeout=10)
        public_ip = response.read().decode('utf-8')
        print(f"✅ Your public IP: {public_ip}")
        return public_ip
    except Exception as e:
        print(f"❌ Failed to get public IP: {e}")
        return None

def main():
    """Main diagnostic function"""
    print("🔍 Network Diagnostic for RDS Database Connection")
    print("=" * 60)
    
    # RDS connection details from logs
    hostname = "kmkz-database-new.co58km6wuv0y.us-east-1.rds.amazonaws.com"
    port = 5432
    
    print(f"Target: {hostname}:{port}")
    print()
    
    # Step 1: Get public IP
    public_ip = get_public_ip()
    print()
    
    # Step 2: Test DNS resolution
    ip_address = test_dns_resolution(hostname)
    if not ip_address:
        print("💥 Cannot proceed without DNS resolution")
        return False
    print()
    
    # Step 3: Test ping
    ping_success = test_ping(hostname)
    print()
    
    # Step 4: Test traceroute
    traceroute_success = test_traceroute(hostname)
    print()
    
    # Step 5: Test TCP connection with different timeouts
    timeouts = [5, 10, 30]
    tcp_success = False
    
    for timeout in timeouts:
        if test_tcp_connection(ip_address, port, timeout):
            tcp_success = True
            break
        print()
    
    # Summary
    print("📋 DIAGNOSTIC SUMMARY")
    print("=" * 30)
    print(f"DNS Resolution: {'✅ PASS' if ip_address else '❌ FAIL'}")
    print(f"Ping Test: {'✅ PASS' if ping_success else '❌ FAIL'}")
    print(f"Traceroute: {'✅ PASS' if traceroute_success else '❌ FAIL'}")
    print(f"TCP Connection: {'✅ PASS' if tcp_success else '❌ FAIL'}")
    print()
    
    if not tcp_success:
        print("🚨 LIKELY CAUSES OF CONNECTION FAILURE:")
        print("1. **Security Group Configuration**")
        print("   - RDS security group doesn't allow inbound connections on port 5432")
        print(f"   - Your IP ({public_ip}) is not in the allowed IP ranges")
        print("   - Check AWS Console > RDS > Security Groups")
        print()
        print("2. **Network Configuration**")
        print("   - RDS instance is in a private subnet without internet gateway")
        print("   - Network ACLs blocking the connection")
        print("   - VPC routing issues")
        print()
        print("3. **RDS Instance Configuration**")
        print("   - RDS instance is not publicly accessible")
        print("   - Database is stopped or in maintenance mode")
        print()
        print("🔧 RECOMMENDED ACTIONS:")
        print("1. Check RDS instance 'Publicly accessible' setting")
        print("2. Verify security group allows port 5432 from your IP")
        print("3. Ensure RDS is in a public subnet with internet gateway")
        print("4. Check if RDS instance is running")
        
    return tcp_success

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\nDiagnostic interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"Diagnostic failed: {e}")
        sys.exit(1)
